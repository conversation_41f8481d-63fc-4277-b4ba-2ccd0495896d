trigger:
  branches:
    include:
      - "feature/*"
      - "Feature/*"
      - "master"
      - "release/*"
      - "Release/*"
resources:
  repositories:
    - repository: PipelineTemplates
      type: git
      name: Zeus\tools.pipeline-templates
      ref: 'refs/heads/master'
stages:
  - template: CI/templates/base-pipeline.yml@PipelineTemplates
    parameters:
      serviceName: 'HyperBackupMgmService'
      branchName: '$(Build.SourceBranch)'
      language: 'go'