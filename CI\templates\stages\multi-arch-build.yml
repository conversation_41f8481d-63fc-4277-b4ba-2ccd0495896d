parameters:
- name: language
  type: string
  default: ''
- name: version
  type: string
  default: ''
- name: pkg_type
  type: string
  default: ''
- name: branchName
  type: string
  default: ''
- name: serviceName
  type: string
  default: ''
- name: pool
  type: string
  default: ''
- name: architecture
  type: string
  default: ''
jobs:
- ${{ if contains(parameters.language, 'go') }}:
  - job: GoBuild
    pool:
      name: ${{ parameters.pool }}
      demands:
        - Agent.OSArchitecture -equals ${{ parameters.architecture }}
        - Agent.OS -equals Linux 
    displayName: "Go 构建"
    dependsOn: SetConfig
    container: $[ variables['buildImage'] ]
    variables:
      buildImage: $[dependencies.SetConfig.outputs['config.buildImage']]
    steps:
      - template: ../steps/build/go-build-arch.yml
        parameters:
          branchName: ${{ parameters.branchName }}
          serviceName: ${{ parameters.serviceName }}
          architecture: ${{ parameters.architecture }}
          version: ${{ parameters.version }}
          pkg_type: ${{ parameters.pkg_type }}
- ${{ if contains(parameters.language, 'python') }}:
  # Python 构建 Job
  - job: PythonBuild
    pool:
      name: ${{ parameters.pool }}
      demands:
        - Agent.OSArchitecture -equals ${{ parameters.architecture }}
        - Agent.OS -equals Linux 
    displayName: "python 构建"
    dependsOn: SetConfig
    container: $[ variables['buildImage'] ]
    variables:
      buildImage: $[dependencies.SetConfig.outputs['config.buildImage']]
    steps:
      - template: ../steps/build/python-build-arch.yml
        parameters:
          branchName: ${{ parameters.branchName }}
          serviceName: ${{ parameters.serviceName }}
          architecture: ${{ parameters.architecture }}
          version: ${{ parameters.version }}
          pkg_type: ${{ parameters.pkg_type }}

- ${{ if contains(parameters.language, 'cpp') }}:
  # C++ 构建 Job
  - job: CppBuild
    pool:
      name: ${{ parameters.pool }}
      demands:
        - Agent.OSArchitecture -equals ${{ parameters.architecture }}
        - Agent.OS -equals Linux 
    displayName: "C++ 构建"
    dependsOn: SetConfig
    container: $[ variables['buildImage'] ]
    variables:
      buildImage: $[dependencies.SetConfig.outputs['config.buildImage']]
    steps:
      - template: ../steps/build/cpp-build-arch.yml
        parameters:
          branchName: ${{ parameters.branchName }}
          serviceName: ${{ parameters.serviceName }}
          architecture: ${{ parameters.architecture }}
          version: ${{ parameters.version }}
          pkg_type: ${{ parameters.pkg_type }}


  # - job: GoBuild
  #   pool:
  #     name: v8buildtest
  #     demands:
  #       - Agent.Name -equals linuxbuilder1  # 指定具体代理
  #   displayName: "Go 构建"
  #   dependsOn: SetConfig
  #   variables:
  #     buildImage: $[dependencies.SetConfig.outputs['config.buildImage']]
  #   steps:
  #     # - checkout: self
  #     - task: Bash@3
  #       name: validateParams
  #       displayName: "验证输入参数"
  #       inputs:
  #         targetType: "inline"
  #         script: |
  #           # 验证必需参数
  #           echo $(language)
  #           echo $(buildImage)
  #           echo $(branchName)
  #           echo $(serviceName)
