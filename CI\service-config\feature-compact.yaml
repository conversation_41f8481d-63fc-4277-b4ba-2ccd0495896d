global:
  # 分支类型：主线是Main，发布分支是Release，feature以及其他分支是Feature，
  branch_type: &branch_type "Feature"
  # 版本：主线是1.0，发布分支自定义版本号如******* ,feature分支以及其他分支用分支号做版本
  Versions:
    system_version: &system_version "{branchName}"

BuildFramework:
  G_BF_BRANCH: master

# 服务配置 - 紧凑格式，按语言分类，保留所有原始服务
services:
  # Go服务 (微服务架构)
  HyperBackupMgmService: { language: go, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CNProtectionService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ThirdPartyService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Programlibrary: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DBTool: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CNResourceManagementService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CNAPIGatewayService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CNSLAStrategyService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CNCacheService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperCDMService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperDataMgmService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperIndexWorkerService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperGatherWorkerService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ProxyMgrService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ProxyService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  MirrorMgrService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  BackupRunnerService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  AgentDiscoveryService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  kafkaconfigtool: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CloudOSBackupService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  EtcdService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  gepms: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DBCreateTool: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  BackupTool: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  KeyMgm: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperResourceCenterActiveDirectoryPlatMgm: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  BackupMgmAD: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  KmcSdk: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Greed: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DrillResourceMgmService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DrmBusinessService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DrillStrategyService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  LogService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ReportService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DependencyConf: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HydraService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DrmReportService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  SilkRoadDataSourceService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  UIDTool: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  NodeMigrationTool: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  LocatesMgmService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperManagedResourceService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  SLAService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  PLogMgmService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HCMSService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CommonResourceMgmService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  certificate_tool: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  OpenSearchService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperRCMService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperRCEService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  NATSService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperResourceCenterVirtualPlatMgm: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperBackupMgmVirtual: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperBackupMgmHost: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperCDMAppHost: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DataMaskingService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  WorkflowMgmService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CapacityMgmService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  WorkflowOrchService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  RepairSDK: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CloudShiftDRService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CloudShiftDRRunnerService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  AMSAuthService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  TimeSeriesDataService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DBDRService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperBackupWorkerService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperSLAMgmService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ApprovalFlowService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DrmVmware: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperSLAWorkerService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperJobMgmService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperJobWorkerService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  OssService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperResourceCenterCloudPlatMgm: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperBackupMgmCloud: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  AStackMgmService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  MultiStorageSvcMgmService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  AntivirusService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperBackupSyncWorkerService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  TemplateMgrService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperBackupMountWorkerService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HSMDomainMgrService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HSMNBUAdapterService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperRCMServiceK8S: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperBackupMgmK8S: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperBackupWorkerK8S: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HRCMExchange: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperBackupMgmExchange: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  BackupWorkerExchange: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  BackupMgmAnyshareAppSystem: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HRCMAnyshareAppSystem: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  OSRecoveryCabinetMgmService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  AppS3Service: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  
  # Python服务 (业务逻辑服务)
  BackupService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ClusterMgmService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ChronydService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  KMSService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  rootWrap: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CommonService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  SelfBackupService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DeploymentService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CDMDispatchService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  UpgradeService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  UpgradeTool: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Common_for_python: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  AMSMessageService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  SNMPService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  AuthService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CDMStoreMgmService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  StorageResMgmService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  SnapSyncService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  LinkMgmService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DataSyncService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Apps_for_python: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ClusterService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DeployNodeMgm: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  IRosProxyService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  JobMonitorService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HADBMgmService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  
  # C++服务 (系统级服务)
  KMCService: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  mariadb: { language: cpp, staticAnalysis: {skip: true}, unitTest: {skip: true}, build: {image: "acr.aishu.cn/ab/mariadb-build:11.8.3-master"} }
  BasicRunner: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Deploy: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ClientFrameWork: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  VirCommonService: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  KeyService: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CNAppAgent: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CNMetadataBackupService: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  kmcsdk_src: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ClientService: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CppServiceCommon: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  cdpcommon: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  eefcommon: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  fcdatachannel_common: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  filesystem_common: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CloudShiftService: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Bingo: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Cache: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  H3CCAS: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Dameng: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Kingbase: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HwCloudGaussDB: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ExchangeServer: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  File: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DataRep: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  FusionCompute: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  FusionOne: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CloudOS: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HCS: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HCSO: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HuaweiCloud: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  HyperV: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  InCloudSphere: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Informix: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  MySQL: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  AnyshareOSS: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  NutanixAHV: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  OpenStack: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  OracleVM: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Gather: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Machine: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  RecoveryCabinet: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  PostgreSQL: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  AnyShare: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ProgramExecutor: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ShenTong: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Gen: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  SmartX: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ProxmoxVE: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  SQLServer: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Sybase: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  VMware: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  AliyunPrivate: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Greenplum: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Draas: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  GaussDB: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Hadoop: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Oracle: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  QingCloud: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  SAPHANA: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  TiDB: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  XenServer: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Volume: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  VolumeRep: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  SPClientService: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DeployClientService: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  JDStack: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Test: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DB2: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ZStack: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  NDM: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Hive: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  VmFileRecovery: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  MongoDB: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  OceanBase: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DBStack: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  GoldenDB: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CNware: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  TCE: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ChiTu: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ApsaraStack: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Sangfor: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ActiveDirectory: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CetcCloud: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  ExchangeEWS: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  WoCloud: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  
  # Shell脚本服务 (工具和配置)
  setup: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Virtuaenv3: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Virtuaenv3_for_update: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Virtuaenv3_for_check: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  Python3: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  KADService: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  WebService: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  DBService: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  cppcommon: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  abthrift: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false} }
  CNGrpcProto: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false} }
