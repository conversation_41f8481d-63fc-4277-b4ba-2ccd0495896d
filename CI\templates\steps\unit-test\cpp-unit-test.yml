parameters:
  coverageEnabled: false
  serviceName: ""
  branchName: ""
steps:
  - task: Bash@3
    displayName: "设置覆盖率配置"
    inputs:
      targetType: "inline"
      script: |
        # 使用传入的覆盖率配置
        COVERAGE_ENABLED=${{ parameters.coverageEnabled }}
        
        echo "覆盖率启用状态: $COVERAGE_ENABLED"
        echo "##vso[task.setvariable variable=coverageEnabled]$COVERAGE_ENABLED"

  - task: Bash@3
    displayName: "构建测试项目"
    inputs:
      targetType: "inline"
      script: |
        # 创建构建目录（libgtest-dev、cmake等工具已预装在镜像中）
        mkdir -p build
        cd build
        
        # 设置CMake选项
        CMAKE_OPTIONS="-DCMAKE_BUILD_TYPE=Debug"
        
        if [ "$(coverageEnabled)" = "true" ]; then
          CMAKE_OPTIONS="$CMAKE_OPTIONS -DCMAKE_CXX_FLAGS='--coverage' -DCMAKE_C_FLAGS='--coverage'"
        fi
        
        # 检查是否存在CMakeLists.txt
        if [ -f "../CMakeLists.txt" ]; then
          echo "使用CMake构建测试..."
          cmake .. $CMAKE_OPTIONS
          make -j$(nproc)
        else
          echo "未找到CMakeLists.txt，尝试手动编译测试文件..."
          
          # 查找测试文件
          TEST_FILES=$(find .. -name "*test*.cpp" -o -name "*Test*.cpp" | head -10)
          
          if [ ! -z "$TEST_FILES" ]; then
            echo "找到测试文件，手动编译..."
            for test_file in $TEST_FILES; do
              test_name=$(basename "$test_file" .cpp)
              echo "编译测试: $test_name"
              
              if [ "$(coverageEnabled)" = "true" ]; then
                g++ --coverage -std=c++11 -lgtest -lgtest_main -pthread "$test_file" -o "$test_name" || echo "编译 $test_name 失败"
              else
                g++ -std=c++11 -lgtest -lgtest_main -pthread "$test_file" -o "$test_name" || echo "编译 $test_name 失败"
              fi
            done
          else
            echo "未找到测试文件"
            exit 1
          fi
        fi
        
        echo "C++ 测试项目构建完成"

  - task: Bash@3
    displayName: "执行 C++ 单元测试"
    inputs:
      targetType: "inline"
      script: |
        cd build
        
        # 创建测试结果目录
        mkdir -p ../test-results
        
        # 查找并运行测试可执行文件
        TEST_EXECUTABLES=$(find . -type f -executable -name "*test*" -o -name "*Test*" | head -10)
        
        if [ -z "$TEST_EXECUTABLES" ]; then
          echo "未找到测试可执行文件"
          exit 1
        fi
        
        echo "找到测试可执行文件: $TEST_EXECUTABLES"
        
        # 运行测试
        OVERALL_EXIT_CODE=0
        for test_exe in $TEST_EXECUTABLES; do
          echo "运行测试: $test_exe"
          
          # 运行测试并生成XML报告
          if ./"$test_exe" --gtest_output=xml:../test-results/$(basename "$test_exe").xml; then
            echo "测试 $test_exe 通过"
          else
            echo "测试 $test_exe 失败"
            OVERALL_EXIT_CODE=1
          fi
        done
        
        # 生成覆盖率报告
        if [ "$(coverageEnabled)" = "true" ] && command -v lcov >/dev/null 2>&1; then
          echo "生成覆盖率报告..."
          
          # 收集覆盖率数据
          lcov --capture --directory . --output-file ../test-results/coverage.info
          
          # 过滤系统文件和测试文件
          lcov --remove ../test-results/coverage.info '/usr/*' '*/test/*' '*/tests/*' --output-file ../test-results/coverage_filtered.info
          
          # 生成HTML报告
          genhtml ../test-results/coverage_filtered.info --output-directory ../test-results/coverage_html
          
          # 显示覆盖率摘要
          echo "覆盖率摘要:"
          lcov --summary ../test-results/coverage_filtered.info
        fi
        
        echo "C++ 单元测试完成"
        exit $OVERALL_EXIT_CODE

  - task: PublishTestResults@2
    displayName: "发布测试结果"
    inputs:
      testResultsFormat: "JUnit"
      testResultsFiles: "**/test-results/*.xml"
      failTaskOnFailedTests: true
    condition: succeededOrFailed()

  - task: PublishCodeCoverageResults@1
    displayName: "发布覆盖率报告"
    inputs:
      codeCoverageToolType: "Cobertura"
      summaryFileLocation: "**/test-results/coverage_filtered.info"
      reportDirectory: "**/test-results/coverage_html"
    condition: and(succeededOrFailed(), eq(variables['coverageEnabled'], 'true'))
