global:
  # 分支类型：主线是Main，发布分支是Release，feature以及其他分支是Feature，
  branch_type: &branch_type "Feature"
  # 版本：主线是1.0，发布分支自定义版本号如******* ,feature分支以及其他分支用分支号做版本
  Versions:
    system_version: &system_version "{branchName}"
BuildFramework:
  G_BF_BRANCH: master
services:
  HyperBackupMgmService:
    language: go
    version: *system_version
    pkg_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
        - name: "arm64"
          enabled: true
  BackupService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
        - name: "arm64"
          enabled: true
  KMCService:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
        - name: "arm64"
          enabled: true
  mariadb:
    language: cpp
    version: *system_version
    product_type: *branch_type
    build:
      architectures:
        - name: "x86"
          enabled: true
          image: "acr.aishu.cn/ab/mariadb-build:11.8.3-master"
        - name: "arm64"
          enabled: true
          image: "acr.aishu.cn/ab/mariadb-build:11.8.3-master"

  ClusterMgmService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
        - name: "arm64"
          enabled: true
  ChronydService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
        - name: "arm64"
          enabled: true
  KMSService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
        - name: "arm64"
          enabled: true
  rootWrap:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
        - name: "arm64"
          enabled: true
  CommonService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
        - name: "arm64"
          enabled: true
  BasicRunner:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
        - name: "arm64"
          enabled: true
  Deploy:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
        - name: "arm64"
          enabled: true
  ClientFrameWork:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
        - name: "arm64"
          enabled: true

  SelfBackupService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
        - name: "arm64"
          enabled: true

  DeploymentService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CDMDispatchService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  UpgradeService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  UpgradeTool:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Common_for_python:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  setup:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Virtuaenv3:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Virtuaenv3_for_update:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Virtuaenv3_for_check:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Python3:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  KADService:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  WebService:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  DBService:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  VirCommonService:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  AMSMessageService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  SNMPService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  AuthService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CDMStoreMgmService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  StorageResMgmService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  SnapSyncService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  LinkMgmService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  DataSyncService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  KeyService:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CNAppAgent:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CNMetadataBackupService:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CNProtectionService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  ThirdPartyService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Programlibrary:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  DBTool:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CNResourceManagementService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CNAPIGatewayService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CNSLAStrategyService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CNCacheService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Apps_for_python:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  kmcsdk_src:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  ClientService:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CppServiceCommon:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  cdpcommon:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  eefcommon:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  fcdatachannel_common:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  filesystem_common:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  cppcommon:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  abthrift:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CNGrpcProto:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  ClusterService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  DeployNodeMgm:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperCDMService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperDataMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperIndexWorkerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperGatherWorkerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  ProxyMgrService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  ProxyService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  MirrorMgrService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  BackupRunnerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  AgentDiscoveryService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  kafkaconfigtool:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CloudOSBackupService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  EtcdService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  gepms:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  DBCreateTool:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  BackupTool:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  IRosProxyService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  KeyMgm:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperResourceCenterActiveDirectoryPlatMgm:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  BackupMgmAD:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  KmcSdk:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Greed:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  DrillResourceMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  DrmBusinessService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  DrillStrategyService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CloudShiftService:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  LogService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  ReportService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  DependencyConf:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HydraService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  JobMonitorService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  DrmReportService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  SilkRoadDataSourceService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  UIDTool:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  NodeMigrationTool:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  LocatesMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperManagedResourceService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  SLAService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Bingo:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Cache:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  H3CCAS:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Dameng:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Kingbase:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HwCloudGaussDB:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  PLogMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  ExchangeServer:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  File:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  DataRep:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  FusionCompute:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  FusionOne:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CloudOS:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HCS:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HCSO:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HuaweiCloud:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperV:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  InCloudSphere:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Informix:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  MySQL:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  AnyshareOSS:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  NutanixAHV:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  OpenStack:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  OracleVM:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Gather:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HADBMgmService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Machine:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  RecoveryCabinet:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  PostgreSQL:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  AnyShare:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  ProgramExecutor:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  ShenTong:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Gen:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  SmartX:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  ProxmoxVE:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  SQLServer:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Sybase:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  VMware:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  AliyunPrivate:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Greenplum:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Draas:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  GaussDB:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Hadoop:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Oracle:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  QingCloud:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  SAPHANA:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  TiDB:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  XenServer:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HCMSService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Volume:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  VolumeRep:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  SPClientService:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  DeployClientService:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  JDStack:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Test:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  DB2:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CommonResourceMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  ZStack:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  NDM:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Hive:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  VmFileRecovery:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  certificate_tool:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  OpenSearchService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperRCMService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperRCEService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  NATSService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  MongoDB:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperResourceCenterVirtualPlatMgm:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperBackupMgmVirtual:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperBackupMgmHost:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperCDMAppHost:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  DataMaskingService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  WorkflowMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CapacityMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  WorkflowOrchService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  RepairSDK:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CloudShiftDRService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CloudShiftDRRunnerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  OceanBase:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  DBStack:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  AMSAuthService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  TimeSeriesDataService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  DBDRService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperBackupWorkerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperSLAMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  ApprovalFlowService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  DrmVmware:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperSLAWorkerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperJobMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperJobWorkerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  GoldenDB:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  OssService:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperResourceCenterCloudPlatMgm:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperBackupMgmCloud:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CNware:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  AStackMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  TCE:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  ChiTu:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  MultiStorageSvcMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  AntivirusService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperBackupSyncWorkerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  TemplateMgrService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperBackupMountWorkerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HSMDomainMgrService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HSMNBUAdapterService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperRCMServiceK8S:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperBackupMgmK8S:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperBackupWorkerK8S:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  ApsaraStack:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  Sangfor:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  ActiveDirectory:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  CetcCloud:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HRCMExchange:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HyperBackupMgmExchange:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  BackupWorkerExchange:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  ExchangeEWS:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  BackupMgmAnyshareAppSystem:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  HRCMAnyshareAppSystem:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  OSRecoveryCabinetMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  AppS3Service:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true

  WoCloud:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true

        - name: "arm64"
          enabled: true
