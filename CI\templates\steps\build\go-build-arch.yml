parameters:
- name: buildImage
  type: string
  default: ''
- name: version
  type: string
  default: ''
- name: pkg_type
  type: string
  default: ''
- name: branchName
  type: string
  default: ''
- name: serviceName
  type: string
  default: ''
- name: pool
  type: string
  default: ''
- name: architecture
  type: string
  default: ''
steps:
  - checkout: self
    fetchDepth: 1  # 只获取最新的1次提交，减少历史记录下载
    clean: true    # 确保工作区干净 
  - task: Bash@3
    displayName: "执行Go构建 "
    inputs:
      targetType: "inline"
      script: |
        # 设置Go环境变量
        pwd && ls -l
        export GOPROXY="https://goproxy.aishu.cn"
        export GOPRIVATE="devops.aishu.cn"
        export GO111MODULE="on"
        export GONOPROXY="devops.aishu.cn"
        export GOSUMDB=off
        export GONOSUMDB="devops.aishu.cn"
        export CI_Pipeline_ID=$(Build.BuildNumber)
        export Service_Name=${{ parameters.serviceName }}
        architecture=$(echo "${{ parameters.architecture }}" | xargs)
        pkg_architecture=""
        if [[ "$architecture" =~ "arm64" || "$architecture" =~ "ARM64" ]]; then
          pkg_architecture="Linux_el7_aarch64"
        elif [[ "$architecture" =~ "x86" || "$architecture" =~ "x64" || "$architecture" =~ "X86" || "$architecture" =~ "X64" ]]; then
          pkg_architecture="Linux_el7_x64"
        else
          echo "未知架构: $architecture"
          exit 1
        fi
        pkgType=$(pkg_type)
        pkgVersion=$(version)
        echo "提取结果: $pkgType/$pkgVersion"
        echo "*********** ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIMxJqdC2+ZHHQAZNtIJZ0G+uWwUohgomh0spjAYdek9a" >> /root/.ssh/known_hosts
        echo "machine devops.aishu.cn" > /root/.netrc
        echo "login <EMAIL>" >> /root/.netrc
        echo "password ixd244okn4jluied6lwfcbpd6ojjkxjpxabu4ykq2xnxyajcofpa" >> /root/.netrc
        export Package_name=$Service_Name-$pkgVersion-$(Build.BuildNumber)-$pkg_architecture.tar.gz
        mkdir ../build
        cd ../build
        cmake ../s || exit 1
        cmake --build . --target run_build || exit 1
        cmake  --build . --target run_package || exit 1
        ls ..
        mkdir -p ../bin
        mv ../$Package_name ../bin
        echo "构建完成，产物位于: $Package_name"
        ls -la ../bin/"$Package_name"

        ftpPath="/ftp_data/FTP/Zeus/service/$pkgType/$Service_Name/$pkgVersion/$pkg_architecture/Release"
        sshpass -p 'Deploy^%$#@!' ssh -o StrictHostKeyChecking=no root@*********** "mkdir -p $ftpPath"
        sshpass -p 'Deploy^%$#@!' scp ../bin/$Package_name root@***********:$ftpPath

  - task: Post-Bash@3
    inputs:
      targetType: inline
      script: |
        rm -rf $(Build.SourcesDirectory)
        ls -lah $(Agent.BuildDirectory)