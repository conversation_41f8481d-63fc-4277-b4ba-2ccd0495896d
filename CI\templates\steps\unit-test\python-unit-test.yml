parameters:
  coverageEnabled: false
  serviceName: "" 
  branchName: ""

steps:
  - task: Bash@3
    displayName: "设置覆盖率配置"
    inputs:
      targetType: "inline"
      script: |
        # 使用传入的覆盖率配置
        COVERAGE_ENABLED=${{ parameters.coverageEnabled }}
        
        echo "覆盖率启用状态: $COVERAGE_ENABLED"
        echo "##vso[task.setvariable variable=coverageEnabled]$COVERAGE_ENABLED"

  - task: Bash@3
    displayName: "安装依赖"
    inputs:
      targetType: "inline"
      script: |
        # 安装项目依赖（pytest和coverage已预装在镜像中）
        if [ -f "requirements.txt" ]; then
          pip install -r requirements.txt
        fi
        
        if [ -f "requirements-test.txt" ]; then
          pip install -r requirements-test.txt
        fi
        
        echo "Python 依赖安装完成"

  - task: Bash@3
    displayName: "执行 Python 单元测试"
    inputs:
      targetType: "inline"
      script: |
        # 检查是否启用覆盖率
        if [ "$(coverageEnabled)" = "true" ]; then
          echo "执行带覆盖率的 pytest 测试..."
          pytest --junitxml=test-results.xml --cov=. --cov-report=xml --cov-report=html
        else
          echo "执行 pytest 测试（无覆盖率）..."
          pytest --junitxml=test-results.xml
        fi
        
        echo "Python 单元测试完成"

  - task: PublishTestResults@2
    displayName: "发布测试结果"
    inputs:
      testResultsFormat: "JUnit"
      testResultsFiles: "**/test-results.xml"
      failTaskOnFailedTests: true
    condition: succeededOrFailed()

  - task: PublishCodeCoverageResults@1
    displayName: "发布覆盖率报告"
    inputs:
      codeCoverageToolType: "Cobertura"
      summaryFileLocation: "**/coverage.xml"
      reportDirectory: "**/htmlcov"
    condition: and(succeededOrFailed(), eq(variables['coverageEnabled'], 'true'))
