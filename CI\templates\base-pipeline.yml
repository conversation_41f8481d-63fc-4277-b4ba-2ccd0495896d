parameters:
  serviceName: ""
  branchName: ""
  language: ""
  # pool: "docker-pool-x86"
  # arm_pool: "docker-pool-arm64"
  pool: "v8buildtest"
  arm_pool: "v8buildtest"
  # 写死的全局配置参数
  ftpServer: "ftp.company.com"
  ftpPort: "21"
  ftpUsername: "deploy_user"
  ftpBasePath: "/uploads"
  sonarUrl: "https://sonar.company.com"
  sonarToken: "squ_1234567890abcdef"
  YQ_IMAGE: "acr.aishu.cn/public/mikefarah/yq:4.26.1"
  images:
    python:
      x86: "acr.aishu.cn/ab/pybuild:1.0"
      arm64: "acr-arm.aishu.cn/ab/pythonbuild:1.0-master"
    go:
      x86: "acr.aishu.cn/ab/gobuild:1.0"
      arm64: "acr-arm.aishu.cn/ab/gobuild:1.0-master"
    cpp:
      x86: "acr.aishu.cn/ab/cppbuild:1.0"
      arm64: "acr-arm.aishu.cn/ab/cppbuild:1.0-master"
stages:
  - stage: Initialize
    displayName: "初始化配置"
    jobs:
      - job: GetConfig
        pool:
          name: ${{ parameters.pool }}
          demands:
            # - Agent.Name -equals linuxbuilder1  # 指定具体代理
            - Agent.OSArchitecture -equals X64
            - Agent.OS -equals Linux   
        container:
          endpoint: acr.aishu.cn
          image: newbuild/staticchecker:latest
          options: --workdir /ci_data/workspcae
        steps:
          - checkout: git://Zeus/tools_pipeline-templates@master
          - task: Bash@3
            name: validateParams
            displayName: "验证输入参数"
            inputs:
              targetType: "inline"
              script: |
                # 验证必需参数
                if [ -z "${{ parameters.serviceName }}" ]; then
                  echo "##vso[task.logissue type=error]错误：serviceName参数不能为空"
                  exit 1
                fi
                
                if [ -z "${{ parameters.branchName }}" ]; then
                  echo "##vso[task.logissue type=error]错误：branchName参数不能为空"
                  exit 1
                fi
                echo "参数验证通过：serviceName=${{ parameters.serviceName }}, branchName=${{ parameters.branchName }}"
          - task: Bash@3
            name: config
            displayName: "解析服务配置"
            inputs:
              targetType: "inline"
              script: |
                # 读取配置文件（yq工具已在离线环境中预装）
                pwd
                ls -l
                # sleep 100s
                SERVICE_CONFIG=""
                # 设置
                if [[ "${{ parameters.branchName }}" =~ "master" ]]; then
                  CONFIG=$(cat CI/service-config/master.yaml)
                  SERVICE_CONFIG=$(echo "$CONFIG" | yq eval '.services."${{ parameters.serviceName }}" | explode(.)' -)
                elif [[ "${{ parameters.branchName }}" =~ "release" || "${{ parameters.serviceName }}" =~ "Release" ]]; then
                  CONFIG=$(cat CI/service-config/release.yaml)
                  SERVICE_CONFIG=$(echo "$CONFIG" | yq eval '.services."${{ parameters.serviceName }}" | explode(.)' -)
                else
                  branchName=${{ parameters.branchName }}
                  branchSuffix="${branchName#refs/heads/}"
                  pkgVersion=$(echo "$branchSuffix" | cut -d'/' -f2)
                  if [[ "$pkgVersion" = "null" || "$pkgVersion" = "" ]]; then
                    echo "##vso[task.logissue type=error]错误：无法从${{ parameters.branchName }}分支中获取版本信息"
                    exit 1
                  fi  
                  sed -i s%{branchName}%${pkgVersion}%g  CI/service-config/feature.yaml
                  CONFIG=$(cat CI/service-config/feature.yaml)
                  SERVICE_CONFIG=$(echo "$CONFIG" | yq eval '.services."${{ parameters.serviceName }}" | explode(.)' -)
                fi
                echo "SERVICE_CONFIG: \n  $SERVICE_CONFIG"

                
                # 验证服务配置是否存在
                if [ "$SERVICE_CONFIG" = "null" ]; then
                  echo "##vso[task.logissue type=error]错误：服务 '${{ parameters.serviceName }}' 在配置文件中不存在"
                  exit 1
                fi
                
                # 从服务配置中获取语言信息
                LANGUAGE=$(echo "$SERVICE_CONFIG" | yq eval '.language' -)
                if [ "$LANGUAGE" = "null" ] || [ -z "$LANGUAGE" ]; then
                  echo "##vso[task.logissue type=error]错误：服务 '${{ parameters.serviceName }}' 缺少语言配置"
                  exit 1
                fi
                version=$(echo "$SERVICE_CONFIG" | yq eval '.version' -)
                pkg_type=$(echo "$SERVICE_CONFIG" | yq eval '.pkg_type' -)

                # 解析各个步骤的 skip 配置
                STATIC_ANALYSIS_SKIP=$(echo "$SERVICE_CONFIG" | yq eval '.staticAnalysis.skip // false' -)
                UNIT_TEST_SKIP=$(echo "$SERVICE_CONFIG" | yq eval '.unitTest.skip // false' -)
                UNIT_TEST_COVERAGE=$(echo "$SERVICE_CONFIG" | yq eval '.unitTest.coverage // false' -)
                BUILD_SKIP=$(echo "$SERVICE_CONFIG" | yq eval '.build.skip // false' -)

                # 解析架构配置
                ENABLED_ARCHITECTURES=$(echo "$SERVICE_CONFIG" | yq eval '.build.architectures[] | select(.enabled == true) | .name' - | tr '\n' ',' | sed 's/,$//')

                # 解析FTP配置
                FTP_ENABLED=$(echo "$SERVICE_CONFIG" | yq eval '.deploy.ftp.enabled // false' -)
                

                
                # 解析服务FTP配置
                PATH_TEMPLATE=$(echo "$SERVICE_CONFIG" | yq eval '.deploy.ftp.pathTemplate' -)
                FILE_PATTERNS=$(echo "$SERVICE_CONFIG" | yq eval '.deploy.ftp.filePatterns[]' - | tr '\n' ',' | sed 's/,$//')
                
                # 解析启用的镜像信息
                ENABLED_ARCHS_JSON=$(echo "$SERVICE_CONFIG" | yq eval '.build.architectures[] | select(.enabled == true) | {"name": .name, "image": .image}' - | yq eval -o=json '.' - | tr '\n' ' ' | sed 's/} {/},{/g')
                X86_Image_NAMES=$(echo "$SERVICE_CONFIG" | yq eval '.build.architectures[] | select(.name == "x86") | .image' - | tr '\n' ',' | sed 's/,$//')
                ARM64_Image_NAMES=$(echo "$SERVICE_CONFIG" | yq eval '.build.architectures[] | select(.name == "arm64") | .image' - | tr '\n' ',' | sed 's/,$//')

                # 设置输出变量
                set +x
                echo "##vso[task.setvariable variable=language;isOutput=true]$LANGUAGE"
                set +x
                echo "##vso[task.setvariable variable=version;isOutput=true]$version"
                set +x
                echo "##vso[task.setvariable variable=pkg_type;isOutput=true]$pkg_type"

                set +x
                echo "##vso[task.setvariable variable=staticAnalysis.skip;isOutput=true]$STATIC_ANALYSIS_SKIP"
                set +x
                echo "##vso[task.setvariable variable=unitTest.skip;isOutput=true]$UNIT_TEST_SKIP"
                set +x
                echo "##vso[task.setvariable variable=unitTest.coverage;isOutput=true]$UNIT_TEST_COVERAGE"
                set +x
                echo "##vso[task.setvariable variable=build.skip;isOutput=true]$BUILD_SKIP"
                set +x
                echo "##vso[task.setvariable variable=build.architectures;isOutput=true]$ENABLED_ARCHITECTURES"
                set +x
                echo "##vso[task.setvariable variable=deploy.ftp.enabled;isOutput=true]$FTP_ENABLED"
                set +x
                echo "##vso[task.setvariable variable=ftp.server;isOutput=true]${{ parameters.ftpServer }}"
                set +x
                echo "##vso[task.setvariable variable=ftp.port;isOutput=true]${{ parameters.ftpPort }}"
                set +x
                echo "##vso[task.setvariable variable=ftp.username;isOutput=true]${{ parameters.ftpUsername }}"
                set +x
                echo "##vso[task.setvariable variable=ftp.basePath;isOutput=true]${{ parameters.ftpBasePath }}"
                set +x
                echo "##vso[task.setvariable variable=ftp.pathTemplate;isOutput=true]$PATH_TEMPLATE"
                set +x
                echo "##vso[task.setvariable variable=ftp.filePatterns;isOutput=true]$FILE_PATTERNS"
                set +x
                echo "##vso[task.setvariable variable=build.imagesList;isOutput=true]$ENABLED_ARCHS_JSON"
                set +x
                echo "##vso[task.setvariable variable=build.x86_image;isOutput=true]$X86_Image_NAMES"
                set +x
                echo "##vso[task.setvariable variable=build.arm64_image;isOutput=true]$ARM64_Image_NAMES"
                set +x
                echo "##vso[task.setvariable variable=sonar.url;isOutput=true]${{ parameters.sonarUrl }}"
                set +x
                echo "##vso[task.setvariable variable=sonar.token;isOutput=true]${{ parameters.sonarToken }}"

                # 调试输出 - 全局参数
                echo "=== 全局参数 ==="
                echo "Service Name: ${{ parameters.serviceName }}"
                echo "Branch Name: ${{ parameters.branchName }}"
                echo "FTP Server: ${{ parameters.ftpServer }}"
                echo "FTP Port: ${{ parameters.ftpPort }}"
                echo "FTP Username: ${{ parameters.ftpUsername }}"
                echo "FTP Base Path: ${{ parameters.ftpBasePath }}"
                echo "Sonar URL: ${{ parameters.sonarUrl }}"
                echo "Sonar Token: ${{ parameters.sonarToken }}"
                
                # 调试输出 - 服务配置参数
                echo "=== 服务配置参数 ==="
                echo "Language: $LANGUAGE"
                echo "version: $version"
                echo "pkg_type: $pkg_type"
                echo "Static Analysis Skip: $STATIC_ANALYSIS_SKIP"
                echo "Unit Test Skip: $UNIT_TEST_SKIP"
                echo "Unit Test Coverage: $UNIT_TEST_COVERAGE"
                echo "Build Skip: $BUILD_SKIP"
                echo "Enabled Architectures: $ENABLED_ARCHITECTURES"
                echo "FTP Upload Enabled: $FTP_ENABLED"
                echo "Path Template: $PATH_TEMPLATE"
                echo "File Patterns: $FILE_PATTERNS"
                echo "Enabled Archs JSON: $ENABLED_ARCHS_JSON"
                echo "X86_Image_NAMES: $X86_Image_NAMES"
                echo "ARM64_Image_NAMES: $ARM64_Image_NAMES"
          - task: Post-Bash@3
            inputs:
              targetType: inline
              script: |
                # docker images -q -f dangling=true | xargs -I {} docker rmi {}
                docker ps -a | grep Exit | cut -d ' ' -f 1 | xargs  docker rm 2>/dev/null| wc -l
                docker volume rm $(docker volume ls -qf dangling=true) 2>/dev/null| wc -l
                rm -rf $(Build.SourcesDirectory)
                ls -lah $(Agent.BuildDirectory)
  - stage: StaticAnalysis
    displayName: "静态代码分析 (仅x64架构)"
    dependsOn: Initialize
    condition: and(succeeded(), ne(stageDependencies.Initialize.GetConfig.outputs['config.staticAnalysis.skip'], 'true'), eq(variables['Agent.OSArchitecture'], 'X64'))
    jobs:
      - template: stages/static-analysis.yml
        parameters:
          serviceName: ${{ parameters.serviceName }}
          branchName: ${{ parameters.branchName }}
          language: $[stageDependencies.Initialize.GetConfig.outputs['config.language']]
          sonarUrl: $[stageDependencies.Initialize.GetConfig.outputs['config.sonar.url']]
          sonarToken: $[stageDependencies.Initialize.GetConfig.outputs['config.sonar.token']]

  - stage: UnitTest
    displayName: "单元测试 (仅x64架构)"
    dependsOn: StaticAnalysis
    condition: and(succeeded(), ne(stageDependencies.Initialize.GetConfig.outputs['config.unitTest.skip'], 'true'), eq(variables['Agent.OSArchitecture'], 'X64'))
    jobs:
      - template: stages/unit-test.yml
        parameters:
          serviceName: ${{ parameters.serviceName }}
          branchName: ${{ parameters.branchName }}
          language: $[stageDependencies.Initialize.GetConfig.outputs['config.language']]
          coverageEnabled: $[stageDependencies.Initialize.GetConfig.outputs['config.unitTest.coverage']]
          
  # - template: stages/multi-arch-build.yml
  #   parameters:
  #     branchName: ${{ parameters.branchName }}
  #     serviceName: ${{ parameters.serviceName }}
  #     language: ${{ parameters.language }}



  # - stage: UnitTest1
  #   displayName: "单元测试test"
  #   dependsOn: 
  #     - Initialize
  #   condition: always()
  #   jobs:
  #     - template: stages/multi-arch-build.yml
  #       parameters:
  #         # configs: $(variables.architecture)
  #         language: ${{ parameters.language }}     





  - stage: BuildX86
    displayName: "X86架构构建"
    condition: always()
    dependsOn: 
      - Initialize
      - UnitTest
    variables:
      language: $[stageDependencies.Initialize.GetConfig.outputs['config.Language']]
      version: $[stageDependencies.Initialize.GetConfig.outputs['config.version']]
      pkg_type: $[stageDependencies.Initialize.GetConfig.outputs['config.pkg_type']]
      enabledArchitectures: $[stageDependencies.Initialize.GetConfig.outputs['config.build.architectures']]
      skip: $[stageDependencies.Initialize.GetConfig.outputs['config.build.skip']]
      image: $[stageDependencies.Initialize.GetConfig.outputs['config.build.x86_image']]
    jobs:
      - job: SetConfig
        displayName: "SetConfig"
        condition: and(eq(variables.skip, 'false'),contains(variables.enabledArchitectures, 'x86'))
        pool:
          name: ${{ parameters.pool }}
          demands:
            # - Agent.Name -equals linuxbuilder1  # 指定具体代理
            - Agent.OSArchitecture -equals X64
            - Agent.OS -equals Linux            
        steps:
          - checkout: none
          - task: Post-Bash@3
            displayName: "Debug: Print stageDependencies"
            name: config
            inputs:
              targetType: inline
              script: |
                  image=$(image)
                  echo $(image)
                  echo $image
                  echo $(skip)
                  echo $(language)
                  echo $(enabledArchitectures)
                  # 根据语言设置镜像（使用parameters中的配置）
                  case $(language) in
                    "python")
                      BUILD_IMAGE="${{ parameters.images.python.x86 }}"
                      ;;
                    "go")
                      BUILD_IMAGE="${{ parameters.images.go.x86 }}"
                      ;;
                    "cpp")
                      BUILD_IMAGE="${{ parameters.images.cpp.x86 }}"
                      ;;
                    *)
                      echo "##vso[task.logissue type=error]不支持的语言: $LANGUAGE"
                      exit 1
                      ;;
                  esac
                  if [[ "$image" != "null" && "$image" != "" ]]; then
                    BUILD_IMAGE=$image
                  fi  
                  set +x
                  echo "##vso[task.setvariable variable=buildImage;isOutput=true]$BUILD_IMAGE"
                  echo "X86 Build Image: $BUILD_IMAGE"
          - task: Post-Bash@3
            inputs:
              targetType: inline
              script: |
                docker ps -a | grep Exit | cut -d ' ' -f 1 | xargs  docker rm 2>/dev/null| wc -l
                docker volume rm $(docker volume ls -qf dangling=true) 2>/dev/null| wc -l
                # docker rmi $(docker images | grep "<none>" | awk '{print $3}') 2>/dev/null| wc -l
                # docker images -q -f dangling=true | xargs -I {} docker rmi {} 2>/dev/null| wc -l

      - template: stages/multi-arch-build.yml
        parameters:
          language: ${{ parameters.language }}
          version: $(variables.version)
          pkg_type: $(variables.pkg_type)
          serviceName: ${{ parameters.serviceName }}
          branchName: ${{ parameters.branchName }}
          pool: ${{ parameters.pool }}
          architecture: 'X64'

          

  - stage: BuildARM64
    displayName: "ARM64构建"
    condition: always()
    dependsOn: 
      - Initialize
      - UnitTest
    variables:
      language: $[stageDependencies.Initialize.GetConfig.outputs['config.Language']]
      version: $[stageDependencies.Initialize.GetConfig.outputs['config.version']]
      pkg_type: $[stageDependencies.Initialize.GetConfig.outputs['config.pkg_type']]
      enabledArchitectures: $[stageDependencies.Initialize.GetConfig.outputs['config.build.architectures']]
      skip: $[stageDependencies.Initialize.GetConfig.outputs['config.build.skip']]
      image: $[stageDependencies.Initialize.GetConfig.outputs['config.build.arm64_image']]
    jobs:
      - job: SetConfig
        displayName: "SetConfig"
        condition: and(eq(variables.skip, 'false'),contains(variables.enabledArchitectures, 'arm64'))
        workspace:
          clean: all
        pool:
          name: ${{ parameters.arm_pool }}  
          demands:
            # - Agent.Name -equals linuxbuilder1  # 指定具体代理
            - Agent.OSArchitecture -equals ARM64
            - Agent.OS -equals Linux 
        steps:
          - checkout: none
          - task: Post-Bash@3
            displayName: "Debug: Print stageDependencies"
            name: config
            inputs:
              targetType: inline
              script: |
                  image=$(image)
                  echo $(image)
                  echo $image
                  echo $(skip)
                  echo $(language)
                  echo $(enabledArchitectures)
                  # 根据语言设置镜像（使用parameters中的配置）
                  case $(language) in
                    "python")
                      BUILD_IMAGE="${{ parameters.images.python.arm64 }}"
                      ;;
                    "go")
                      BUILD_IMAGE="${{ parameters.images.go.arm64 }}"
                      ;;
                    "cpp")
                      BUILD_IMAGE="${{ parameters.images.cpp.arm64 }}"
                      ;;
                    *)
                      echo "##vso[task.logissue type=error]不支持的语言: $LANGUAGE"
                      exit 1
                      ;;
                  esac
                  if [[ "$image" != "null" && "$image" != "" ]]; then
                    BUILD_IMAGE=$image
                  fi  
                  set +x
                  echo "##vso[task.setvariable variable=buildImage;isOutput=true]$BUILD_IMAGE"
                  echo "arm64 Build Image: $BUILD_IMAGE"
          - task: Post-Bash@3
            inputs:
              targetType: inline
              script: |
                docker ps -a | grep Exit | cut -d ' ' -f 1 | xargs  docker rm 2>/dev/null| wc -l
                docker volume rm $(docker volume ls -qf dangling=true) 2>/dev/null| wc -l
                # docker rmi $(docker images | grep "<none>" | awk '{print $3}') 2>/dev/null| wc -l
                # docker images -q -f dangling=true | xargs -I {} docker rmi {} 2>/dev/null| wc -l

      - template: stages/multi-arch-build.yml
        parameters:
          language: ${{ parameters.language }}   
          version: $(variables.version)
          pkg_type: $(variables.pkg_type)
          serviceName: ${{ parameters.serviceName }}
          branchName: ${{ parameters.branchName }}
          pool: ${{ parameters.arm_pool }}
          architecture: 'ARM64'

    

