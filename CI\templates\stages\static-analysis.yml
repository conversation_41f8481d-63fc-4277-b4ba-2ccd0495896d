parameters:
  serviceName: ""
  branchName: ""
  language: ""
  sonarUrl: ""
  sonarToken: ""

jobs:
  - job: StaticAnalysis
    displayName: "静态代码分析"
    pool: v8buildtest
    steps:
      # 不需要重复下载代码，使用工作区中已有的代码

      - ${{ if eq(parameters.language, 'python') }}:
          - template: ../steps/static-analysis/python-static-analysis.yml
            parameters:
              serviceName: ${{ parameters.serviceName }}
              branchName: ${{ parameters.branchName }}
              sonarUrl: ${{ parameters.sonarUrl }}
              sonarToken: ${{ parameters.sonarToken }}

      - ${{ if eq(parameters.language, 'go') }}:
          - template: ../steps/static-analysis/go-static-analysis.yml
            parameters:
              serviceName: ${{ parameters.serviceName }}
              branchName: ${{ parameters.branchName }}
              sonarUrl: ${{ parameters.sonarUrl }}
              sonarToken: ${{ parameters.sonarToken }}

      - ${{ if eq(parameters.language, 'cpp') }}:
          - template: ../steps/static-analysis/cpp-static-analysis.yml
            parameters:
              serviceName: ${{ parameters.serviceName }}
              branchName: ${{ parameters.branchName }}
              sonarUrl: ${{ parameters.sonarUrl }}
              sonarToken: ${{ parameters.sonarToken }}
