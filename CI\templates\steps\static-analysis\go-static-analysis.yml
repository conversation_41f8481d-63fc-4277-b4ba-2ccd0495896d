parameters:
  serviceName: ""
  branchName: ""
  sonarUrl: ""
  sonarToken: ""

steps:
  - task: Bash@3
    displayName: "生成 Sonar 配置文件"
    inputs:
      targetType: "inline"
      script: |
        # 创建 sonar-project.properties 文件
        cat > sonar-project.properties << EOF
        sonar.projectKey=${{ parameters.serviceName }}
        sonar.projectName=${{ parameters.serviceName }}
        sonar.projectVersion=$(Build.BuildNumber)
        sonar.sources=.
        sonar.language=go
        sonar.sourceEncoding=UTF-8
        sonar.go.coverage.reportPaths=coverage.out
        sonar.exclusions=**/*test*/**,**/vendor/**
        EOF
        
        echo "Sonar 配置文件已生成"
        cat sonar-project.properties

  - task: Bash@3
    displayName: "执行 Sonar 静态分析"
    inputs:
      targetType: "inline"
      script: |
        # 执行 Sonar 扫描（工具已预装在镜像中）
        sonar-scanner \
          -Dsonar.host.url=${{ parameters.sonarUrl }} \
          -Dsonar.login=${{ parameters.sonarToken }} \
          -Dsonar.projectBaseDir=$(Build.SourcesDirectory)
        
        echo "Go Sonar 静态分析完成"

  - task: PublishBuildArtifacts@1
    displayName: "发布静态分析结果"
    inputs:
      pathToPublish: ".scannerwork"
      artifactName: "go-static-analysis-results"
      publishLocation: "Container"
