#!/usr/bin/env python3
"""
脚本用于将feature.yaml转换为紧凑格式，保留所有原始key和value
"""

import yaml
import sys

def convert_to_compact_format():
    # 读取原始feature.yaml文件
    with open('CI/service-config/feature.yaml', 'r', encoding='utf-8') as f:
        data = yaml.safe_load(f)
    
    # 创建新的紧凑格式内容
    compact_content = []
    
    # 添加头部信息
    compact_content.append('global:')
    compact_content.append('  # 分支类型：主线是Main，发布分支是Release，feature以及其他分支是Feature，')
    compact_content.append('  branch_type: &branch_type "Feature"')
    compact_content.append('  # 版本：主线是1.0，发布分支自定义版本号如8.0.7.1 ,feature分支以及其他分支用分支号做版本')
    compact_content.append('  Versions:')
    compact_content.append('    system_version: &system_version "{branchName}"')
    compact_content.append('')
    compact_content.append('BuildFramework:')
    compact_content.append('  G_BF_BRANCH: master')
    compact_content.append('')
    compact_content.append('# 服务配置 - 紧凑格式，保留所有原始key和value')
    compact_content.append('services:')
    
    # 处理每个服务
    services = data.get('services', {})
    
    for service_name, service_config in services.items():
        # 构建紧凑格式的服务配置
        compact_service = []
        
        # 语言
        if 'language' in service_config:
            compact_service.append(f'language: {service_config["language"]}')
        
        # 版本
        if 'version' in service_config:
            compact_service.append('version: *system_version')
        
        # 产品类型 (注意HyperBackupMgmService使用pkg_type)
        if 'product_type' in service_config:
            compact_service.append('product_type: *branch_type')
        elif 'pkg_type' in service_config:
            compact_service.append('pkg_type: *branch_type')
        
        # 静态分析
        if 'staticAnalysis' in service_config:
            static_analysis = service_config['staticAnalysis']
            skip_value = static_analysis.get('skip', False)
            compact_service.append(f'staticAnalysis: {{skip: {str(skip_value).lower()}}}')
        
        # 单元测试
        if 'unitTest' in service_config:
            unit_test = service_config['unitTest']
            skip_value = unit_test.get('skip', False)
            compact_service.append(f'unitTest: {{skip: {str(skip_value).lower()}}}')
        
        # 构建配置
        if 'build' in service_config:
            build_config = service_config['build']
            if 'architectures' in build_config:
                archs = build_config['architectures']
                arch_list = []
                for arch in archs:
                    arch_dict = f'{{name: "{arch["name"]}", enabled: {str(arch["enabled"]).lower()}'
                    if 'image' in arch:
                        arch_dict += f', image: "{arch["image"]}"'
                    arch_dict += '}'
                    arch_list.append(arch_dict)
                compact_service.append(f'build: {{architectures: [{", ".join(arch_list)}]}}')
        
        # 组合成一行
        service_line = f'  {service_name}: {{ {", ".join(compact_service)} }}'
        compact_content.append(service_line)
    
    # 写入新文件
    with open('CI/service-config/feature-compact.yaml', 'w', encoding='utf-8') as f:
        f.write('\n'.join(compact_content))
    
    print(f"已生成紧凑格式文件，包含 {len(services)} 个服务")

if __name__ == '__main__':
    convert_to_compact_format()
