parameters:
- name: buildImage
  type: string
  default: ''
- name: version
  type: string
  default: ''
- name: pkg_type
  type: string
  default: ''
- name: branchName
  type: string
  default: ''
- name: serviceName
  type: string
  default: ''
- name: pool
  type: string
  default: ''
- name: architecture
  type: string
  default: ''
steps:
  - checkout: self
    fetchDepth: 1  # 只获取最新的1次提交，减少历史记录下载
    clean: true    # 确保工作区干净 
  - task: Bash@3
    displayName: "执行Python构建 "
    # container: ${{ parameters.buildImage }}
    inputs:
      targetType: "inline"
      script: |
        # 设置环境变量

        export CI_Pipeline_ID=$(Build.BuildNumber)
        export Service_Name=${{ parameters.serviceName }}
        

        # 设置架构
        architecture=$(echo "${{ parameters.architecture }}" | xargs)
        pkg_architecture=""
        if [[ "$architecture" =~ "arm64" || "$architecture" =~ "ARM64" ]]; then
          pkg_architecture="Linux_el7_aarch64"
        elif [[ "$architecture" =~ "x86" || "$architecture" =~ "x64" || "$architecture" =~ "X86" || "$architecture" =~ "X64" ]]; then
          pkg_architecture="Linux_el7_x64"
        else
          echo "未知架构: $architecture"
          exit 1
        fi
        
        branchName=${{ parameters.branchName }}
        branchSuffix="${branchName#refs/heads/}"
        
        # 定义默认值
        pkgType="Unknown"
        pkgVersion="1.0"
        
        # 处理已知分支模式
        case "$branchSuffix" in
        master)
        pkgType="Main"
        G_BF_BRANCH="master"
        pkgVersion="1.0"
        ;;
        release/*)
        pkgType="Release"
        pkgVersion=$(echo "$branchSuffix" | cut -d'/' -f2)
        ;;
        Feature/*)
        pkgType="Feature"
        pkgVersion=$(echo "$branchSuffix" | cut -d '-' -f2)
        G_BF_BRANCH="master"
        ;;
        *)
        # 提取第一部分作为包类型
        pkgType=$(echo "$branchSuffix" | cut -d'/' -f1)
        
        # 尝试从最后部分提取版本
        lastSegment=$(echo "$branchSuffix" | rev | cut -d'/' -f1 | rev)
        if [[ "$lastSegment" == *"-"* ]]; then
        pkgVersion=${lastSegment##*-}
        fi
        ;;
        esac
        
        # 设置输出变量
        echo "##vso[task.setvariable variable=pkgType]$pkgType"
        echo "##vso[task.setvariable variable=pkgVersion]$pkgVersion"
        echo "*********** ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIMxJqdC2+ZHHQAZNtIJZ0G+uWwUohgomh0spjAYdek9a" >> /root/.ssh/known_hosts
        echo "提取结果: $pkgType/$pkgVersion"
        export Package_name=$Service_Name-$pkgVersion-$(Build.BuildNumber)-$pkg_architecture
        echo "machine devops.aishu.cn" > /root/.netrc
        echo "login <EMAIL>" >> /root/.netrc
        echo "password ixd244okn4jluied6lwfcbpd6ojjkxjpxabu4ykq2xnxyajcofpa" >> /root/.netrc
        git clone https://devops.aishu.cn/AISHUDevOps/Zeus/_git/buildframework -b ${G_BF_BRANCH}
        pwd 
        ls -l
        mv buildframework/* ci
        chmod -R +x ci/apollo/cmake/tools/*.sh
        cd ci/apollo/cmake/tools
        source ./abenv-${pkg_architecture}.sh
        cd  ../../../../src/${Service_Name}
        makec.sh -j4 Release package -DCPACK_PACKAGE_FILE_NAME=${Package_name}
        ftpPath="/ftp_data/FTP/Zeus/service/$pkgType/$Service_Name/$pkgVersion/$pkg_architecture/Release"
        pwd
        sshpass -p 'Deploy^%$#@!' ssh -o StrictHostKeyChecking=no root@*********** "mkdir -p $ftpPath"
        sshpass -p 'Deploy^%$#@!' scp ../../ci/package/$pkg_architecture/Release/${Package_name}.tar.gz root@***********:$ftpPath

  - task: Post-Bash@3
    inputs:
      targetType: inline
      script: |
        docker images -q -f dangling=true | xargs -I {} docker rmi {}
        rm -rf $(Build.SourcesDirectory)
        ls -lah $(Agent.BuildDirectory)