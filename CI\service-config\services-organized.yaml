global:
  # 分支类型：主线是Main，发布分支是Release，feature以及其他分支是Feature，
  branch_type: &branch_type "Release"
  # 版本：主线是1.0，发布分支自定义版本号如******* ,feature分支以及其他分支用分支号做版本
  Versions:
    system_version: &system_version "*******"
    product_version: &product_version "*******"

# 服务配置 - 按语言分类，只保留必要的业务配置
services:
  # Python服务 (微服务架构)
  UserService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  OrderService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  PaymentService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  NotificationService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  AuthService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  ConfigService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  ReportService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  AnalyticsService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  FileService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  SearchService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  
  # Go服务 (高性能服务)
  HyperBackupMgmService: { language: go, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  BackupService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  KMCService: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  SvrMgmService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  ClusterMgmService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  ChronydService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  KMSService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  rootWrap: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  CommonService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  BasicRunner: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  Deploy: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  ClientFrameWork: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  
  # 数据库相关服务
  mariadb: { language: cpp, staticAnalysis: {skip: true}, unitTest: {skip: true}, build: {image: "acr.aishu.cn/ab/mariadb-build:11.8.3-master"} }
  DBService: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 60} }
  DBTool: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  DBCreateTool: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  
  # Web相关服务
  WebService: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 60} }
  KADService: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 60} }
  
  # 系统工具服务
  setup: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 50} }
  Virtuaenv3: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 50} }
  Virtuaenv3_for_update: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 50} }
  Virtuaenv3_for_check: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 50} }
  Python3: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 50} }
  
  # 云原生服务
  CNAppAgent: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  CNMetadataBackupService: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  CNProtectionService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  CNResourceManagementService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  CNAPIGatewayService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  CNSLAStrategyService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  CNCacheService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 第三方集成服务
  ThirdPartyService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  Programlibrary: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  
  # 存储相关服务
  CDMStoreMgmService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  StorageResMgmService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  SnapSyncService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  LinkMgmService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  DataSyncService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 安全相关服务
  KeyService: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  KeyMgm: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  KmcSdk: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 监控和日志服务
  AMSMessageService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  SNMPService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  LogService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  JobMonitorService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 备份相关服务
  SelfBackupService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  DeploymentService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  CDMDispatchService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  UpgradeService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  UpgradeTool: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  BackupRunnerService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  BackupTool: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  CloudOSBackupService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  BackupMgmAD: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 虚拟化平台服务
  VirCommonService: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  HyperV: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  VMware: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  OpenStack: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  FusionCompute: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  FusionOne: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  CloudOS: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  HCS: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  HCSO: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  HuaweiCloud: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  InCloudSphere: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  NutanixAHV: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  OracleVM: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  
  # 数据库平台服务
  MySQL: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  PostgreSQL: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  Informix: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  Dameng: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  Kingbase: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  HwCloudGaussDB: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  H3CCAS: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  
  # 文件系统服务
  File: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  AnyShare: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  AnyshareOSS: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  
  # 数据复制服务
  DataRep: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  
  # Exchange服务
  ExchangeServer: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  
  # 机器和恢复服务
  Machine: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  RecoveryCabinet: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  
  # 数据采集服务
  Gather: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  
  # 缓存服务
  Cache: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  Bingo: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  
  # 云迁移服务
  CloudShiftService: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  
  # 高可用数据库管理
  HADBMgmService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 公共库和框架
  Common_for_python: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  Apps_for_python: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  kmcsdk_src: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  ClientService: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  CppServiceCommon: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  cdpcommon: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  eefcommon: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  fcdatachannel_common: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  filesystem_common: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 70} }
  cppcommon: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 50} }
  abthrift: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 50} }
  CNGrpcProto: { language: shell, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 50} }
  
  # 集群管理服务
  ClusterService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  DeployNodeMgm: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 超融合服务
  HyperCDMService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  HyperDataMgmService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  HyperIndexWorkerService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  HyperGatherWorkerService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  HyperManagedResourceService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  HyperResourceCenterActiveDirectoryPlatMgm: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 代理服务
  ProxyMgrService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  ProxyService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  IRosProxyService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 镜像管理服务
  MirrorMgrService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 代理发现服务
  AgentDiscoveryService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # Kafka配置工具
  kafkaconfigtool: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # Etcd服务
  EtcdService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 性能监控服务
  gepms: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 贪心算法服务
  Greed: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 演练资源管理服务
  DrillResourceMgmService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  DrmBusinessService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  DrillStrategyService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  DrmReportService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 依赖配置服务
  DependencyConf: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # Hydra服务
  HydraService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 丝绸之路数据源服务
  SilkRoadDataSourceService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 工具服务
  UIDTool: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  NodeMigrationTool: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 位置管理服务
  LocatesMgmService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # SLA服务
  SLAService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
  
  # 日志管理服务
  PLogMgmService: { language: golang, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 75} }
