# Azure DevOps CI/CD 流水线模板仓库

这是一个企业级的 Azure DevOps CI/CD 流水线模板仓库，为其他代码库提供标准化的构建、测试和部署模板。支持多语言（Python、Go、C++）和多架构（x86、ARM64）的自动化流水线。

## 🏗️ 项目架构

### 模板仓库结构

```
tools_pipeline-templates/                    # 模板仓库根目录
├── README.md                               # 项目说明文档
├── service-config.yml                      # 全局服务配置文件
├── example-azure-pipelines.yml             # 其他代码库引用示例
├── CI/                                     # CI 模板目录
│   ├── templates/                          # 核心模板文件
│   │   ├── base-pipeline.yml               # 主流水线模板（入口）
│   │   ├── stages/                         # 阶段级模板
│   │   │   ├── static-analysis.yml         # 静态分析阶段
│   │   │   ├── unit-test.yml               # 单元测试阶段
│   │   │   └── multi-arch-build.yml        # 多架构构建阶段
│   │   └── steps/                          # 步骤级模板
│   │       ├── static-analysis/            # 各语言静态分析步骤
│   │       │   ├── python-static-analysis.yml
│   │       │   ├── go-static-analysis.yml
│   │       │   └── cpp-static-analysis.yml
│   │       ├── unit-test/                  # 各语言单元测试步骤
│   │       │   ├── python-unit-test.yml
│   │       │   ├── go-unit-test.yml
│   │       │   └── cpp-unit-test.yml
│   │       └── build/                      # 各语言构建步骤
│   │           ├── python-build-arch.yml
│   │           ├── go-build-arch.yml
│   │           └── cpp-build-arch.yml
│   ├── scripts/                            # 辅助脚本
│   ├── docs/                               # 文档
│   └── service-config.yml                  # CI 专用配置
└── CD/                                     # CD 模板目录（预留）
```

### 架构设计原则

1. **分层模板设计**: 采用三层架构（Pipeline → Stage → Step）
2. **配置驱动**: 所有服务配置通过 `service-config.yml` 统一管理
3. **模板复用**: 最大化代码复用，减少维护成本
4. **架构隔离**: 静态分析和测试仅在 x64 执行，构建支持多架构
5. **企业适配**: 完全适配内网环境，所有工具预装在镜像中

## 🚀 实现方案


### 核心组件说明

#### 1. base-pipeline.yml（主模板）
- **功能**: 流水线入口，参数验证，配置解析
- **输入参数**:
  - `serviceName`: 服务名称（必需）
  - `branchName`: 分支名称（必需）
  - `language`: 编程语言（必需）
- **内置配置**: FTP服务器、SonarQube、构建镜像等企业级配置

#### 2. Stage 级模板
- **static-analysis.yml**: 静态代码分析（仅 x64 架构）
- **unit-test.yml**: 单元测试和覆盖率（仅 x64 架构）
- **multi-arch-build.yml**: 多架构并行构建

#### 3. Step 级模板
- **语言特定**: 每种语言有独立的静态分析、测试、构建步骤
- **架构特定**: 支持 x86 和 ARM64 的交叉编译

## 📋 其他代码库如何使用

### 步骤 1: 创建 azure-pipelines.yml

在你的代码库根目录创建 `azure-pipelines.yml` 文件，参考 `example-azure-pipelines.yml`：

```yaml
trigger:
  branches:
    include:
      - "feature/*"
      - "Feature/*"
      - "master"
      - "release/*"
      - "Release/*"

resources:
  repositories:
    - repository: PipelineTemplates
      type: git
      name: Zeus/tools.pipeline-templates    # 模板仓库路径
      ref: 'refs/heads/master'               # 使用的分支

stages:
  - template: CI/templates/base-pipeline.yml@PipelineTemplates
    parameters:
      serviceName: 'YourServiceName'         # 你的服务名称
      branchName: '$(Build.SourceBranch)'    # 当前分支
      language: 'python'                     # python/go/cpp
```

### 步骤 2: 在模板仓库中配置你的服务

在模板仓库的 `CI/service-config.yml` 中添加你的服务配置：

```yaml
services:
  YourServiceName:                          # 与 azure-pipelines.yml 中的 serviceName 一致
    language: python                        # 必需：python/go/cpp
    staticAnalysis:
      skip: false                           # 是否跳过静态分析
    unitTest:
      skip: false                           # 是否跳过单元测试
      coverage: true                        # 是否启用覆盖率
    build:
      architectures:                        # 构建架构配置
        - name: "x86"                       # 架构名称
          enabled: true                     # 是否启用该架构
          artifactName: "YourService-python-x86"  # 产物名称
        - name: "arm64"
          enabled: true
          artifactName: "YourService-python-arm64"
```

### 实际配置示例

参考模板仓库中的现有配置：

```yaml
# CI/service-config.yml
services:
  HyperBackupMgmService:
    language: go
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
      coverage: true
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "HyperBackupMgmService-go-x86"
        - name: "arm64"
          enabled: true
          artifactName: "HyperBackupMgmService-go-arm64"
```

### 步骤 3: 确保代码库结构符合要求

#### Python 项目结构
```
your-python-service/
├── azure-pipelines.yml          # 流水线配置
├── setup.py                     # 必需：用于构建
├── requirements.txt             # 必需：依赖列表
├── requirements-test.txt        # 可选：测试依赖
├── src/                         # 源代码目录
└── tests/                       # 测试代码目录
```

#### Go 项目结构
```
your-go-service/
├── azure-pipelines.yml          # 流水线配置
├── go.mod                       # 必需：Go 模块文件
├── go.sum                       # 必需：依赖校验
├── main.go                      # 主程序入口
├── pkg/                         # 包目录
└── *_test.go                   # 测试文件
```

#### C++ 项目结构
```
your-cpp-service/
├── azure-pipelines.yml          # 流水线配置
├── CMakeLists.txt              # 必需：CMake 构建文件
├── src/                        # 源代码目录
├── include/                    # 头文件目录
└── tests/                      # 测试代码目录
```

## ⚙️ 配置字段说明

### 服务配置结构

```yaml
services:
  ServiceName:
    language: string                        # 必需字段
    staticAnalysis:
      skip: boolean                         # 可选，默认 false
    unitTest:
      skip: boolean                         # 可选，默认 false
      coverage: boolean                     # 可选，默认 true
    build:
      architectures:                        # 必需字段
        - name: string                      # "x86" 或 "arm64"
          enabled: boolean                  # 是否启用该架构
          artifactName: string              # 产物名称
```

### 字段详细说明

| 字段路径 | 类型 | 必需 | 说明 | 示例值 |
|----------|------|------|------|--------|
| `language` | string | ✅ | 编程语言类型 | `python`, `go`, `cpp` |
| `staticAnalysis.skip` | boolean | ❌ | 是否跳过静态分析 | `false` |
| `unitTest.skip` | boolean | ❌ | 是否跳过单元测试 | `false` |
| `unitTest.coverage` | boolean | ❌ | 是否启用覆盖率 | `true` |
| `build.architectures[].name` | string | ✅ | 架构名称 | `x86`, `arm64` |
| `build.architectures[].enabled` | boolean | ✅ | 是否启用该架构 | `true`, `false` |
| `build.architectures[].artifactName` | string | ✅ | 构建产物名称 | `Service-lang-arch` |

> **注意**: 配置文件中**不包含**以下字段，这些都在模板中内置处理：
> - `build.commands`: 构建命令由模板根据语言类型自动生成
> - `build.image`: 构建镜像在模板中预配置
> - `deploy`: 部署配置在模板中内置
> - `staticAnalysis.tools`: 静态分析工具由模板根据语言自动选择
> - `staticAnalysis.options`: 工具选项在模板中预配置

### 各语言配置示例

#### Python 服务
```yaml
services:
  UserService:
    language: python
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
      coverage: true
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "UserService-python-x86"
        - name: "arm64"
          enabled: true
          artifactName: "UserService-python-arm64"
```

#### Go 服务
```yaml
services:
  OrderService:
    language: go
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
      coverage: true
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-go-x86"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-go-arm64"
```

#### C++ 服务
```yaml
services:
  PaymentService:
    language: cpp
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
      coverage: true
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "PaymentService-cpp-x86"
        - name: "arm64"
          enabled: false                    # C++ 可能不需要 ARM64
          artifactName: "PaymentService-cpp-arm64"
```

## 🔧 流水线参数说明

### 必需参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `serviceName` | string | 服务名称，必须在模板仓库的 `CI/service-config.yml` 中存在 | `'HyperBackupMgmService'` |
| `branchName` | string | 分支名称，用于下载指定分支的代码 | `'$(Build.SourceBranch)'` |
| `language` | string | 编程语言类型 | `'python'`, `'go'`, `'cpp'` |

### 可选参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `pool` | string | `'v8buildtest'` | x86 构建代理池名称 |
| `arm_pool` | string | `'v8buildtest'` | ARM64 构建代理池名称 |

### 内置企业配置

模板中已内置以下企业级配置（无需外部配置）：

```yaml
# FTP 服务器配置
ftpServer: "ftp.company.com"
ftpPort: "21"
ftpUsername: "deploy_user"
ftpBasePath: "/uploads"

# SonarQube 配置
sonarUrl: "https://sonar.company.com"
sonarToken: "squ_1234567890abcdef"

# 容器镜像仓库
YQ_IMAGE: "acr.aishu.cn/public/mikefarah/yq:4.26.1"
```

## 🏗️ 技术实现

### 流水线执行策略

| 阶段 | 执行架构 | 原因 | 执行条件 |
|------|----------|------|----------|
| 初始化 | x64 | 配置解析和参数验证 | 总是执行 |
| 静态分析 | 仅 x64 | 分析工具兼容性，避免重复 | 可配置跳过 |
| 单元测试 | 仅 x64 | 测试逻辑与架构无关 | 可配置跳过 |
| 构建 | x86 + ARM64 | 生成不同架构的产物 | 并行执行 |
| 部署 | 单次 | 统一收集所有架构产物 | 构建完成后 |

### 工具链矩阵

| 语言 | 静态分析工具 | 测试框架 | 构建工具 | 输出格式 |
|------|-------------|----------|----------|----------|
| Python | flake8, pylint | pytest | setuptools | wheel, tar.gz |
| Go | golint, go vet | go test | go build | binary |
| C++ | cppcheck, clang-tidy | gtest | cmake, make | binary, .so |

### 预配置构建镜像

| 语言 | x86 镜像 | ARM64 镜像 |
|------|----------|------------|
| Python | `python:3.9-slim` | `python:3.9-slim` |
| Go | `acr.aishu.cn/newbuild/gobuilder:1.0` | `acr-arm.aishu.cn/newbuild/gobuilder:1.0` |
| C++ | `gcc:11-alpine` | `gcc:11-alpine` |

## � 使用示例

### 单服务项目

```yaml
# your-service/azure-pipelines.yml
trigger:
  branches:
    include:
      - master
      - feature/*
      - release/*

resources:
  repositories:
    - repository: PipelineTemplates
      type: git
      name: Zeus/tools.pipeline-templates
      ref: 'refs/heads/master'

stages:
  - template: CI/templates/base-pipeline.yml@PipelineTemplates
    parameters:
      serviceName: 'UserService'
      branchName: '$(Build.SourceBranch)'
      language: 'python'
```

### 多服务项目（Monorepo）

```yaml
# monorepo/azure-pipelines.yml
trigger:
  branches:
    include:
      - master
      - feature/*
      - release/*

resources:
  repositories:
    - repository: PipelineTemplates
      type: git
      name: Zeus/tools.pipeline-templates
      ref: 'refs/heads/master'

stages:
  # Python 用户服务
  - template: CI/templates/base-pipeline.yml@PipelineTemplates
    parameters:
      serviceName: 'UserService'
      branchName: '$(Build.SourceBranch)'
      language: 'python'

  # Go 订单服务
  - template: CI/templates/base-pipeline.yml@PipelineTemplates
    parameters:
      serviceName: 'OrderService'
      branchName: '$(Build.SourceBranch)'
      language: 'go'

  # C++ 支付服务
  - template: CI/templates/base-pipeline.yml@PipelineTemplates
    parameters:
      serviceName: 'PaymentService'
      branchName: '$(Build.SourceBranch)'
      language: 'cpp'
```

### 条件执行示例

```yaml
# 基于分支条件执行
stages:
  - template: CI/templates/base-pipeline.yml@PipelineTemplates
    parameters:
      serviceName: 'UserService'
      branchName: '$(Build.SourceBranch)'
      language: 'python'
    condition: |
      or(
        eq(variables['Build.SourceBranch'], 'refs/heads/master'),
        startsWith(variables['Build.SourceBranch'], 'refs/heads/release/'),
        startsWith(variables['Build.SourceBranch'], 'refs/heads/feature/')
      )
```

## 🎯 最佳实践

### 服务命名规范

- 使用 PascalCase：`UserService`、`OrderService`
- 避免特殊字符和空格
- 保持简洁且具有描述性
- 与实际服务名称保持一致

### 分支策略建议

```yaml
trigger:
  branches:
    include:
      - master                 # 主分支
      - develop               # 开发分支
      - release/*             # 发布分支
      - hotfix/*              # 热修复分支
      - feature/*             # 功能分支
    exclude:
      - feature/experimental  # 排除实验性分支
```

### 配置管理建议

1. **集中管理**: 所有服务配置在模板仓库的 `CI/service-config.yml` 中统一管理
2. **版本控制**: 配置变更通过 PR 进行审核
3. **环境隔离**: 不同环境使用不同的配置分支
4. **安全配置**: 敏感信息使用 Azure DevOps 变量组管理

## 🔧 故障排除

### 常见问题

#### 1. 服务配置未找到
```
错误: 服务 'XXXService' 在配置文件中不存在
解决方案:
1. 检查 serviceName 参数拼写
2. 确保在模板仓库的 CI/service-config.yml 中存在对应配置
3. 验证配置文件语法正确
```

#### 2. 模板仓库引用失败
```
错误: Repository 'PipelineTemplates' could not be found
解决方案:
1. 检查仓库路径: Zeus/tools.pipeline-templates
2. 确保有访问模板仓库的权限
3. 验证分支引用是否正确
```

#### 3. 构建镜像拉取失败
```
错误: Unable to pull image 'acr.aishu.cn/...'
解决方案:
1. 检查容器镜像仓库连接配置
2. 验证镜像路径和标签
3. 确认代理池有访问镜像仓库的权限
```

#### 4. 参数验证失败
```
错误: serviceName参数不能为空
解决方案:
1. 检查 azure-pipelines.yml 中的参数配置
2. 确保所有必需参数都已提供
3. 验证参数值格式正确
```

### 调试技巧

1. **启用详细日志**
   - 查看流水线执行日志
   - 检查每个阶段的输出

2. **本地验证**
   ```bash
   # 验证配置文件语法
   yq eval '.services.YourService' CI/service-config.yml

   # 本地测试构建命令
   python setup.py build    # Python
   go build                 # Go
   cmake . && make          # C++
   ```

3. **分阶段调试**
   ```yaml
   # 临时跳过某些阶段
   staticAnalysis:
     skip: true
   unitTest:
     skip: true
   ```

## 🛠️ 模板维护指南

### 添加新语言支持

1. **创建语言特定的步骤模板**
   ```
   CI/templates/steps/
   ├── static-analysis/
   │   └── newlang-static-analysis.yml
   ├── unit-test/
   │   └── newlang-unit-test.yml
   └── build/
       └── newlang-build-arch.yml
   ```

2. **更新主模板配置**
   - 在 `base-pipeline.yml` 中添加新语言的镜像配置
   - 更新条件判断逻辑

3. **更新文档**
   - 在 README.md 中添加新语言的使用说明
   - 更新配置示例

### 版本管理策略

- **主分支 (master)**: 稳定版本，生产环境使用
- **开发分支 (develop)**: 开发版本，测试新功能
- **功能分支 (feature/*)**: 新功能开发
- **发布分支 (release/*)**: 版本发布准备

## 📝 版本历史与特性

### 当前版本特性

- ✅ **多语言支持**: Python、Go、C++ 项目的统一构建流程
- ✅ **多架构构建**: x86 和 ARM64 架构的并行构建
- ✅ **模板化设计**: 三层架构（Pipeline → Stage → Step）
- ✅ **配置驱动**: 通过 service-config.yml 统一管理所有服务
- ✅ **企业级特性**: 内置 FTP 部署、SonarQube 集成
- ✅ **离线环境**: 完全适配企业内网环境
- ✅ **代码质量**: 集成静态分析和单元测试
- ✅ **架构优化**: 静态分析和测试仅在 x64 执行，构建支持多架构

### 架构演进

1. **v1.0**: 基础多语言构建支持
2. **v2.0**: 添加多架构构建和配置驱动
3. **v3.0**: 企业级特性集成和模板化重构
4. **当前版本**: 完善的文档和最佳实践指南

## 🤝 贡献指南

### 如何贡献

1. **Fork 模板仓库**
2. **创建功能分支**: `feature/your-feature-name`
3. **提交更改**: 遵循提交规范
4. **创建 Pull Request**: 详细描述变更内容

### 代码规范

- **YAML 格式**: 使用 2 个空格缩进
- **命名约定**: 保持一致的 PascalCase 或 kebab-case
- **注释**: 为复杂逻辑添加说明注释
- **模板结构**: 遵循现有的三层架构设计

### 提交规范

```
type(scope): description

类型:
- feat: 新功能
- fix: 修复 bug
- docs: 文档更新
- refactor: 代码重构
- perf: 性能优化
- test: 测试相关
```

## 📞 支持与联系

### 获取帮助

- **📖 文档**: 查看本 README 和 CI/docs/ 目录下的详细文档
- **🐛 问题反馈**: 通过模板仓库的 Issues 提交 bug 报告
- **💡 功能建议**: 通过 Issues 提交新功能建议
- **❓ 使用咨询**: 在 Issues 中提问使用相关问题

### 联系方式

- **模板仓库**: `Zeus/tools.pipeline-templates`
- **维护团队**: DevOps 平台团队
- **更新频率**: 每月定期更新

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

---

## 🎯 快速参考

### 核心文件

- `CI/templates/base-pipeline.yml`: 主流水线模板
- `CI/service-config.yml`: 服务配置文件
- `example-azure-pipelines.yml`: 使用示例

### 关键参数

- `serviceName`: 服务名称（必需）
- `branchName`: 分支名称（必需）
- `language`: 编程语言（必需）

### 支持的语言

- Python (pytest, flake8, pylint)
- Go (go test, golint, go vet)
- C++ (gtest, cppcheck, clang-tidy)

---

**🎉 感谢使用 Azure DevOps CI/CD 流水线模板！**

如果这个项目对你有帮助，请考虑给我们一个 ⭐ Star！