
global:
# 分支类型：主线是Main，发布分支是Release，feature以及其他分支是Feature，
  branch_type: &branch_type "Release"
# 版本：主线是1.0，发布分支自定义版本号如******* ,feature分支以及其他分支用分支号做版本
  Versions:
    system_version: &system_version "*******"
    product_version: &product_version "*******"
# 服务配置 - 按语言分类，只保留必要的业务配置
services:
  # Python服务 (微服务架构)
  UserService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  OrderService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  PaymentService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  NotificationService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  AuthService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  ConfigService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  ReportService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  AnalyticsService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  FileService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }
  SearchService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: 80} }

  SelfBackupService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DeploymentService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CDMDispatchService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  UpgradeService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  UpgradeTool:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Common_for_python:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  setup:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Virtuaenv3:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Virtuaenv3_for_update:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Virtuaenv3_for_check:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Python3:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  mariadb:
    language: cpp
    version: *system_version
    product_type: *branch_type
    build:
      architectures:
        - name: "x86"
          enabled: true
          image: "acr.aishu.cn/ab/mariadb-build:11.8.3-master"
        - name: "arm64"
          enabled: true
          image: "acr.aishu.cn/ab/mariadb-build:11.8.3-master"

  KADService:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  WebService:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DBService:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  VirCommonService:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  AMSMessageService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  SNMPService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  AuthService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CDMStoreMgmService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  StorageResMgmService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  SnapSyncService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  LinkMgmService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DataSyncService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  KeyService:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CNAppAgent:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CNMetadataBackupService:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CNProtectionService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  ThirdPartyService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Programlibrary:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DBTool:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CNResourceManagementService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CNAPIGatewayService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CNSLAStrategyService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CNCacheService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Apps_for_python:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  kmcsdk_src:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  ClientService:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CppServiceCommon:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  cdpcommon:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  eefcommon:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  fcdatachannel_common:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  filesystem_common:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  cppcommon:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  abthrift:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CNGrpcProto:
    language: shell
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  ClusterService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DeployNodeMgm:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperCDMService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperDataMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperIndexWorkerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperGatherWorkerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  ProxyMgrService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  ProxyService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  MirrorMgrService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  BackupRunnerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  AgentDiscoveryService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  kafkaconfigtool:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CloudOSBackupService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  EtcdService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  gepms:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DBCreateTool:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  BackupTool:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  IRosProxyService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  KeyMgm:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperResourceCenterActiveDirectoryPlatMgm:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  BackupMgmAD:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  KmcSdk:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Greed:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DrillResourceMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DrmBusinessService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DrillStrategyService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CloudShiftService:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  LogService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  ReportService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DependencyConf:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HydraService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  JobMonitorService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DrmReportService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  SilkRoadDataSourceService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  UIDTool:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  NodeMigrationTool:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  LocatesMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperManagedResourceService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  SLAService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Bingo:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Cache:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  H3CCAS:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Dameng:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Kingbase:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HwCloudGaussDB:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  PLogMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  ExchangeServer:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  File:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DataRep:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  FusionCompute:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  FusionOne:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CloudOS:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HCS:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HCSO:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HuaweiCloud:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperV:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  InCloudSphere:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Informix:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  MySQL:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  AnyshareOSS:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  NutanixAHV:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  OpenStack:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  OracleVM:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Gather:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HADBMgmService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Machine:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  RecoveryCabinet:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  PostgreSQL:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  AnyShare:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  ProgramExecutor:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  ShenTong:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Gen:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  SmartX:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  ProxmoxVE:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  SQLServer:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Sybase:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  VMware:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  AliyunPrivate:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Greenplum:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Draas:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  GaussDB:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Hadoop:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Oracle:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  QingCloud:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  SAPHANA:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  TiDB:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  XenServer:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HCMSService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Volume:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  VolumeRep:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  SPClientService:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DeployClientService:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  JDStack:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Test:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DB2:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CommonResourceMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  ZStack:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  NDM:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Hive:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  VmFileRecovery:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  certificate_tool:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  OpenSearchService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperRCMService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperRCEService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  NATSService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  MongoDB:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperResourceCenterVirtualPlatMgm:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperBackupMgmVirtual:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperBackupMgmHost:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperCDMAppHost:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DataMaskingService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  WorkflowMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CapacityMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  WorkflowOrchService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  RepairSDK:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CloudShiftDRService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CloudShiftDRRunnerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  OceanBase:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DBStack:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  AMSAuthService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  TimeSeriesDataService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DBDRService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperBackupWorkerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperSLAMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  ApprovalFlowService:
    language: python
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  DrmVmware:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperSLAWorkerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperJobMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperJobWorkerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  GoldenDB:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  OssService:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperResourceCenterCloudPlatMgm:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperBackupMgmCloud:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CNware:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  AStackMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  TCE:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  ChiTu:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  MultiStorageSvcMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  AntivirusService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperBackupSyncWorkerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  TemplateMgrService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperBackupMountWorkerService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HSMDomainMgrService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HSMNBUAdapterService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperRCMServiceK8S:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperBackupMgmK8S:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperBackupWorkerK8S:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  ApsaraStack:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  Sangfor:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  ActiveDirectory:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  CetcCloud:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HRCMExchange:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HyperBackupMgmExchange:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  BackupWorkerExchange:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  ExchangeEWS:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  BackupMgmAnyshareAppSystem:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  HRCMAnyshareAppSystem:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  OSRecoveryCabinetMgmService:
    language: golang
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  AppS3Service:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"

  WoCloud:
    language: cpp
    version: *system_version
    product_type: *branch_type
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-cpp-arm64"
