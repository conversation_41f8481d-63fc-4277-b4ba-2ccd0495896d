
global:
  # 分支类型：主线是Main，发布分支是Release，feature以及其他分支是Feature，
  branch_type: &branch_type "Release"
  # 版本：主线是1.0，发布分支自定义版本号如8.0.7.1 ,feature分支以及其他分支用分支号做版本
  Versions:
    system_version: &system_version "1.5.6"
    product_version: &product_version "1.5.6"
services:
  mariadb:
    language: cpp
    version: *system_version
    product_type: *branch_type
    build:
      architectures:
        - name: "x86"
          enabled: true
          image: "acr.aishu.cn/ab/mariadb-build:11.8.3-master"
        - name: "arm64"
          enabled: true
          image: "acr.aishu.cn/ab/mariadb-build:11.8.3-master"